<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择建设单位</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .dashboard {
            display: flex;
            width: 1080px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            min-height: 700px;
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
            max-width: none;
            width: 830px;
        }

        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            max-width: none;
            min-height: 600px;
        }

        .card h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #1a237e;
            font-size: 16px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .button {
            padding: 8px 15px;
            background: linear-gradient(45deg, #4a90e2, #63b8ff);
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .button:hover {
            background: linear-gradient(45deg, #3a80d2, #53a8ff);
        }

        /* Checkbox 相关样式 */
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            width: 100%;
            margin: 40px 0 30px 0;
            padding: 0 10px;
        }

        .checkbox-item {
            position: relative;
            min-height: 36px;
        }

        .checkbox-item input[type="checkbox"] {
            display: none;
        }

        .checkbox-item label {
            display: flex;
            align-items: center;
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            color: #333;
            background: #fff;
            transition: all 0.3s ease;
            position: relative;
            padding: 0 12px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        .checkbox-item input[type="checkbox"]:checked + label {
            border-color: #4a90e2;
            color: #4a90e2;
        }

        .checkbox-item input[type="checkbox"]:checked + label::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 20px 20px;
            border-color: transparent transparent #4a90e2 transparent;
        }

        /* 表单相关样式 */
        .two-columns-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
            padding: 0 10px;
        }

        .blocks {
            position: relative;
            width: calc(50% - 10px);
            display: flex;
            align-items: center;
            min-height: 36px;
            margin-bottom: 20px;
        }

        .titles {
            color: #4a90e2;
            width: 120px;
            font-size: 14px;
            flex-shrink: 0;
            text-align: left;
            margin-right: 10px;
        }

        .selects, .inputs {
            flex: 1;
            height: 36px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
            color: #333;
            background-color: #fff;
            outline: none;
            transition: all 0.3s;
        }

        .selects {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 30px;
        }

        .selected-item .selects,
        .selected-item .inputs {
            border-color: #4a90e2;
            background-color: #f8f9ff;
        }

        .btn-submit {
            display: block;
            width: 200px;
            height: 36px;
            margin: 40px auto 20px;
            background: #4a90e2;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-submit:hover {
            background: #357abd;
        }

        select:disabled, input:disabled {
            background-color: #f5f5f5;
            border-color: #e0e0e0;
            color: #999;
            cursor: not-allowed;
        }
    </style>
</head>
	<body>
    <div class="dashboard">
        <div class="main-content">
            <div class="card content-section active" id="section1">
                <h3>一、选择必要参数</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox1">
                        <label for="checkbox1">南方电网</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox2">
                        <label for="checkbox2">广东电网公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox3">
                        <label for="checkbox3">广西电网公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox4">
                        <label for="checkbox4">云南电网公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox5">
                        <label for="checkbox5">贵州电网公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox6">
                        <label for="checkbox6">海南电网公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox7">
                        <label for="checkbox7">深圳供电局</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox8">
                        <label for="checkbox8">南网超高压公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox50">
                        <label for="checkbox50">南网储能公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox51">
                        <label for="checkbox51">南网产业投资集团</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox52">
                        <label for="checkbox52">鼎元资产公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox53">
                        <label for="checkbox53">南网能源公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox54">
                        <label for="checkbox54">南网国际（香港）公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox55">
                        <label for="checkbox55">南网澜湄国际公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox56">
                        <label for="checkbox56">南网资本控股公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox57">
                        <label for="checkbox57">南网财务公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox58">
                        <label for="checkbox58">鼎和保险公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox59">
                        <label for="checkbox59">南网党校</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox60">
                        <label for="checkbox60">南网北京分公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox61">
                        <label for="checkbox61">南网共享公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox62">
                        <label for="checkbox62">南网生态运营公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox63">
                        <label for="checkbox63">南网数字集团</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox64">
                        <label for="checkbox64">南网供应链集团</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox65">
                        <label for="checkbox65">南网能源院</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox66">
                        <label for="checkbox66">南网科研院</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox67">
                        <label for="checkbox67">广州电力交易中心</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox68">
                        <label for="checkbox68">南网传媒公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox69">
                        <label for="checkbox69">北京研究院</label>
                    </div>
                </div>
				
				<div class="two-columns-container">
					<div class="blocks block-select-3">
                        <div class="titles">项目分类</div>
                        <select class="selects select-3">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="信息系统建设与升级改造">信息系统建设与升级改造</option>
                            <!--<option value="信息基础设施建设与升级改造">信息基础设施建设与升级改造</option>
                            <option value="信息安全防护体系建设与升级改造">信息安全防护体系建设与升级改造</option>
                            <option value="运行维护">运行维护</option>-->
                            <option value="信息专题研究">信息专题研究</option>
                        </select>
                    </div>
					
					<div class="blocks block-select-12">
                        <div class="titles">建设周期T（月）</div>
                        <select class="selects select-12">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="一年内">一年内</option>
                            <option value="一年以上两年内">一年以上两年内</option>
                            <option value="两年以上">两年以上</option>
                        </select>
                    </div>
					
                    <div class="blocks block-select-1">
                        <div class="titles">项目类型</div>
                        <select class="selects select-1">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="应用系统类">应用系统类</option>
                            <option value="技术支持平台">技术支持平台</option>
                            <option value="数据分析应用">数据分析应用</option>
                            <option value="移动应用类">移动应用类</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>                  

                    <div class="blocks block-select-7">
                        <div class="titles">建设性质</div>
                        <select class="selects select-7">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="新建">新建</option>
                            <option value="升级改造">升级改造</option>
                        </select>
                    </div>

                    <div class="blocks block-select-9">
                        <div class="titles">系统等保级别</div>
                        <select class="selects select-9">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="一级（含互联网用户）">一级（含互联网用户）</option>
                            <option value="一级（不含互联网用户）">一级（不含互联网用户）</option>
                            <option value="二级（含互联网用户）">二级（含互联网用户）</option>
                            <option value="二级（不含互联网用户）">二级（不含互联网用户）</option>
                            <option value="三级">三级</option>
                            <option value="四级">四级</option>
                        </select>
                    </div>

                    <div class="blocks block-input-10">
                        <div class="titles">系统用户数量</div>
                        <input class="inputs input-10" type="number" min="1" placeholder="请输入系统用户数量">
                    </div>

                    <div class="blocks block-select-11">
                        <div class="titles">系统部署方式</div>
                        <select class="selects select-11">
                            <option value="" disabled selected>选择一个选项</option>
                            <option value="网一级部署模式">网一级部署模式</option>
                            <option value="网省两级部署模式">网省两级部署模式</option>
                            <option value="省一级部署模式">省一级部署模式</option>
                            <option value="省地两级部署模式">省地两级部署模式</option>
                            <option value="省地县三级部署模式">省地县三级部署模式</option>
                            <option value="网一级管理节点和省一级分节点部署模式">网一级管理节点和省一级分节点部署模式</option>
                        </select>
                    </div>
                </div>

                <button type="button" class="btn-submit" onclick="submit()">确定</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化依赖字段
            initializeDependentFields();
            
            // 添加事件监听器
            addEventListeners();
            
            // 请求初始状态
            requestInitialCheckboxStates();
        });

        function initializeDependentFields() {
            const dependentBlocks = [
                '.block-select-1',
                '.block-select-7',
                '.block-select-9',
                '.block-input-10',
                '.block-select-11'
            ];

            dependentBlocks.forEach(selector => {
                const block = document.querySelector(selector);
                if (block) {
                    block.style.display = 'none';
                    const element = block.querySelector('select, input');
                    if (element) {
                        element.disabled = true;
                    }
                }
            });
        }

        function addEventListeners() {
            // 项目分类变化监听
            const select3 = document.querySelector('.select-3');
            if (select3) {
                select3.addEventListener('change', handleProjectTypeChange);
            }

            // select 和 input 变化监听
            document.querySelectorAll('.selects, .inputs').forEach(element => {
                element.addEventListener('change', () => handleItemSelection(element));
            });

            // checkbox1（南方电网公司）选中时自动选中省级公司
            const checkbox1 = document.getElementById('checkbox1');
            if (checkbox1) {
                checkbox1.addEventListener('change', function () {
                    if (this.checked) {
                        // 选中所有省级公司（广东到海南）
                        for (let i = 2; i <= 6; i++) {
                            const checkbox = document.getElementById(`checkbox${i}`);
                            if (checkbox) {
                                checkbox.checked = true;
                                // 通知后端状态变化
                                const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                                window.chrome.webview.postMessage({
                                    type: 'checkbox',
                                    id: checkbox.id,
                                    checked: true,
                                    label: label
                                });
                            }
                        }
                    } else {
                        // 当取消选中南方电网时，取消所有其他复选框的选中状态
                        const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
                        checkboxes.forEach(checkbox => {
                            if (checkbox.id !== 'checkbox1' && checkbox.checked) {
                                checkbox.checked = false;
                                const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                                window.chrome.webview.postMessage({
                                    type: 'checkbox',
                                    id: checkbox.id,
                                    checked: false,
                                    label: label
                                });
                            }
                        });
                    }
                });
            }

            // 为所有复选框添加单选逻辑
            document.querySelectorAll('.checkbox-group input[type="checkbox"]').forEach(function (checkbox) {
                checkbox.addEventListener('change', function () {
                    // 如果当前复选框被选中且不是南方电网
                    if (this.checked && this.id !== 'checkbox1') {
                        // 检查南方电网是否被选中
                        const checkbox1 = document.getElementById('checkbox1');
                        if (!checkbox1.checked) {
                            // 如果南方电网未选中，则确保只有当前复选框被选中
                            const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
                            checkboxes.forEach(otherCheckbox => {
                                if (otherCheckbox.id !== this.id && otherCheckbox.id !== 'checkbox1' && otherCheckbox.checked) {
                                    otherCheckbox.checked = false;
                                    const label = document.querySelector(`label[for="${otherCheckbox.id}"]`).textContent;
                                    window.chrome.webview.postMessage({
                                        type: 'checkbox',
                                        id: otherCheckbox.id,
                                        checked: false,
                                        label: label
                                    });
                                }
                            });
                        }
                    }

                    // 原有的消息发送逻辑
                    const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                    window.chrome.webview.postMessage({
                        type: 'checkbox',
                        id: this.id,
                        checked: this.checked,
                        label: label
                    });
                });
            });

            // 全选和反选按钮
            const selectAllBtn = document.querySelector('.select-all');
            const invertSelectionBtn = document.querySelector('.invert-selection');

            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function () {
                    // 全选时，确保南方电网被选中，这样才能多选
                    const checkbox1 = document.getElementById('checkbox1');
                    checkbox1.checked = true;

                    const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = true;
                        const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                        window.chrome.webview.postMessage({
                            type: 'checkbox',
                            id: checkbox.id,
                            checked: true,
                            label: label
                        });
                    });
                });
            }

            if (invertSelectionBtn) {
                invertSelectionBtn.addEventListener('click', function () {
                    const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = !checkbox.checked;
                        const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                        window.chrome.webview.postMessage({
                            type: 'checkbox',
                            id: checkbox.id,
                            checked: checkbox.checked,
                            label: label
                        });
                    });
                    // 如果反选后南方电网未被选中
                    if (wasChecked1 && !checkbox1.checked) {
                        // 检查是否有多个选中项
                        const checkedBoxes = Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked'));
                        if (checkedBoxes.length > 1) {
                            // 保留第一个选中项，取消其他选中项
                            for (let i = 1; i < checkedBoxes.length; i++) {
                                checkedBoxes[i].checked = false;
                                const label = document.querySelector(`label[for="${checkedBoxes[i].id}"]`).textContent;
                                window.chrome.webview.postMessage({
                                    type: 'checkbox',
                                    id: checkedBoxes[i].id,
                                    checked: false,
                                    label: label
                                });
                            }
                        }
                    }
                });
            }
        }

        // 添加两个辅助函数
        function getCheckedCheckboxes() {
            return Array.from(document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked'));
        }

        function keepOnlyOneChecked(checkboxToKeep) {
            const checkboxes = document.querySelectorAll('.checkbox-group input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
                if (checkbox !== checkboxToKeep) {
                    checkbox.checked = false;
                    // 通知后端状态变化
                    const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                    window.chrome.webview.postMessage({
                        type: 'checkbox',
                        id: checkbox.id,
                        checked: false,
                        label: label
                    });
                }
            });
        }

        document.querySelectorAll('input[type="checkbox"]').forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: 'checkbox',  // 在选择建设单位页面使用
                    id: this.id,
                    checked: this.checked,
                    label: label
                });
            });
        });

        function requestInitialState() {
            window.chrome.webview.postMessage({ type: 'getFormState' });
        }

        function updateFormState(state) {
            if (!state) return;

            // 更新项目分类
            const select3 = document.querySelector('.select-3');
            if (select3 && state.select_3) {
                select3.value = state.select_3;
                handleProjectTypeChange();
                handleItemSelection(select3);
            }

            // 更新建设周期
            const select12 = document.querySelector('.select-12');
            if (select12 && state.select_12) {
                select12.value = state.select_12;
                handleItemSelection(select12);
            }

            // 如果是信息系统建设与升级改造，更新额外字段
            if (state.select_3 === '信息系统建设与升级改造') {
                // 更新项目类型
                const select1 = document.querySelector('.select-1');
                if (select1 && state.select_1) {
                    select1.value = state.select_1;
                    handleItemSelection(select1);
                }

                // 更新建设性质
                const select7 = document.querySelector('.select-7');
                if (select7 && state.select_7) {
                    select7.value = state.select_7;
                    handleItemSelection(select7);
                }

                // 更新系统等保级别
                const select9 = document.querySelector('.select-9');
                if (select9 && state.select_9) {
                    select9.value = state.select_9;
                    handleItemSelection(select9);
                }

                // 更新系统用户数量
                const input10 = document.querySelector('.input-10');
                if (input10 && state.input_10) {
                    input10.value = state.input_10;
                    handleItemSelection(input10);
                }

                // 更新系统部署方式
                const select11 = document.querySelector('.select-11');
                if (select11 && state.select_11) {
                    select11.value = state.select_11;
                    handleItemSelection(select11);
                }
            }
        }

        function handleProjectTypeChange() {
            const select3 = document.querySelector('.select-3');
            const dependentBlocks = [
                '.block-select-1',
                '.block-select-7',
                '.block-select-9',
                '.block-input-10',
                '.block-select-11'
            ];

            const isInfoSystemType = select3.value === '信息系统建设与升级改造';

            dependentBlocks.forEach(selector => {
                const block = document.querySelector(selector);
                const element = block.querySelector('select, input');

                if (isInfoSystemType) {
                    block.style.display = 'flex';
                    element.disabled = false;
                } else {
                    block.style.display = 'none';
                    element.disabled = true;
                    element.value = '';
                    block.classList.remove('selected-item');
                }
            });

            saveFormState();
        }

        function handleItemSelection(element) {
            if (element.tagName === 'SELECT') {
                const parentBlock = element.closest('.blocks');
                if (element.value) {
                    parentBlock.classList.add('selected-item');
                } else {
                    parentBlock.classList.remove('selected-item');
                }
            } else if (element.tagName === 'INPUT') {
                const parentBlock = element.closest('.blocks');
                if (element.value.trim()) {
                    parentBlock.classList.add('selected-item');
                } else {
                    parentBlock.classList.remove('selected-item');
                }
            }

            saveFormState();
        }

        function saveFormState() {
            // 获取系统用户数量的值
            const input10Value = document.querySelector('.input-10').value;

            // 验证输入是否为有效数字
            const input10Number = input10Value ? parseFloat(input10Value) : 0;

            if (input10Value && (isNaN(input10Number) || input10Number <= 0)) {
                alert('系统用户数量必须是正整数');
                return;
            }

            const formState = {
                type: 'saveFormState',
                data: {
                    select_3: document.querySelector('.select-3').value,
                    select_12: document.querySelector('.select-12').value,
                    select_1: document.querySelector('.select-1').value,
                    select_7: document.querySelector('.select-7').value,
                    select_9: document.querySelector('.select-9').value,
                    input_10: input10Number, // 使用转换后的数字
                    select_11: document.querySelector('.select-11').value
                }
            };

            window.chrome.webview.postMessage(formState);
        }

        function submit() {
            const select3Value = document.querySelector('.select-3').value;
            const select12Value = document.querySelector('.select-12').value;

            if (!select3Value || !select12Value) {
                alert('请填写项目分类和建设周期！');
                return;
            }

            let formData = {
                type: 'formSubmit',
                data: {
                    select_3: select3Value,
                    select_12: select12Value
                }
            };

            if (select3Value === '信息系统建设与升级改造') {
                const additionalFields = {
                    select_1: document.querySelector('.select-1').value,
                    select_7: document.querySelector('.select-7').value,
                    select_9: document.querySelector('.select-9').value,
                    input_10: document.querySelector('.input-10').value,
                    select_11: document.querySelector('.select-11').value
                };

                for (let key in additionalFields) {
                    if (!additionalFields[key]) {
                        alert('请填写所有必要参数！');
                        return;
                    }
                }

                Object.assign(formData.data, additionalFields);
            }

            window.chrome.webview.postMessage(formData);

            // 添加关闭表单的消息，确保即使表单提交失败也能关闭窗口
            setTimeout(() => {
                window.chrome.webview.postMessage({ type: 'closeForm' });
            }, 100);
        }

        // 添加一个直接关闭表单的函数
        function closeForm() {
            window.chrome.webview.postMessage({ type: 'closeForm' });
        }

        // 请求初始状态
        function requestInitialCheckboxStates() {
            try {
                // 这会触发WebView_NavigationCompleted回调
                window.chrome.webview.postMessage({ type: "getInitialState" });
            } catch (error) {
                // 静默处理错误
            }
        }

        // 这个函数会被C#端调用来设置复选框状态
        function restoreCheckboxStates(states) {
            for (let id in states) {
                const checkbox = document.getElementById(id);
                if (checkbox) {
                    checkbox.checked = states[id];
                }
            }
        }
    </script>
</body>
</html>