<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="Ribbon_Load">
	<ribbon>
		<tabs>
			<tab id="tab1" label="有解助手">
				<group id="group1" label="账户">
					<splitButton id="splitButton1" size="large" getEnabled="GetSplitButton1Enabled" >
						<button id="splitButton1__btn" screentip="登录" supertip="点击登录" onAction="SplitButton1_Click" getLabel="GetSplitButton1Label" getImage="GetUserImage"/>
						<menu id="splitButton1__mnu" screentip="登录">
							<button id="button1" onAction="Button1_Click" label="个人中心" getEnabled="GetControlEnabled" getImage="GetHomeImage" visible="false"/>
							<button id="button2" onAction="Button2_Click" label="退出登录" getEnabled="GetControlEnabled" getImage="GetLogoutImage"/>
						</menu>
					</splitButton>
				</group>
				<group id="group2" label="人机协同">
					<button id="button3" onAction="Button3_Click" screentip="智能生成" supertip="根据已上传的厂家资料，生成完整的文档" label="智能生成&#xA;" size="large" getEnabled="GetControlEnabled" getImage="GetAIGenerateImage"/>
					<button id="button4" onAction="Button4_Click" screentip="优化" supertip="选中所需优化的标题以及文本内容" label="优化&#xA;" size="large" getEnabled="GetControlEnabled" getImage="GetPolishImage"/>
					<button id="button5" onAction="Button5_Click" screentip="扩写" supertip="不修改选中的文本内容，续写选中的文本" label="扩写&#xA;" size="large" getEnabled="GetControlEnabled" getImage="GetExpandImage"/>
					<button id="button6" onAction="Button6_Click" screentip="精简" supertip="对选中的文本进行精炼表述" label="精简&#xA;" size="large" getEnabled="GetControlEnabled" getImage="GetSimplifyImage"/>
				</group>
				<group id="group3" label="机器代人">
					<box id="box3">
						<button id="button30" onAction="button30_Click" screentip="项目名称" supertip="项目名称" label="项目名称" size="normal" getImage="GetProjectNameImage" getEnabled="GetControlEnabled"/>
						<comboBox id="comboBox1" sizeString="WWWWWWWWWWWWWWWWWWWWWWWWWWWWW" onChange="ComboBox1_TextChanged" getItemLabel="GetComboBoxItemLabel" getText="GetComboBoxText" getItemCount="GetComboBoxItemCount" supertip="输入或选择可研报告名称" showImage="false" getEnabled="GetControlEnabled">
							<item id="__id1" label="可研报告1" />
							<item id="__id2" label="可研报告2" />
							<item id="__id3" label="可研报告3" />
							<item id="__id4" label="可研报告4,这一条用来测试显示的长度" />
						</comboBox>
					</box>
					<box id="box1">
						<button id="button7" onAction="Button7_Click" screentip="上传" supertip="上传文档参考的其他文档或模板" label="上传参考文件" getEnabled="GetControlEnabled" getImage="GetUploadImage"/>

						<menu id="menu1" label="已上传文件列表" getEnabled="GetControlEnabled" getImage="GetFileMenuImage">
							<button id="button12"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button13"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button14"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button15"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button16"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>

							<button id="button24"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button25"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button26"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button27"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
							<button id="button28"  onAction="DeleteUploadedFile" getLabel="GetFileLabel" getImage="GetFileImage" getScreentip="GetFileScreentip" getVisible="GetButtonVisible"/>
						</menu>
						
						<button id="button8" onAction="Button8_Click" label="调整必要参数" getEnabled="GetGenerateEnabled" getImage="GetEnterpriseImage"/>
						<button id="button9" onAction="Button9_Click" label="选择生成章节" getEnabled="GetGenerateEnabled" getImage="GetMenuImage"/>
						
					</box>
					<box id="box2">
						<checkBox id="checkBox1" tag="checkBox1" onAction="CheckBox1_Click" getPressed="GetCheckBox1Pressed" label="同步生成估算书" getEnabled="GetGenerateEnabled"/>
						<editBox id="editBox1" screentip="期望投资金额（万元）：" supertip="勾选同步生成估算书，给投资一个期望的金额上限" label="期望投资金额（万元）：" sizeString="WWWWWWWWW" enabled="false" getImage="GetBudgetImage" getText="GetEditBox1Text" onChange="EditBox1_TextChanged"/>
					</box>

					<button id="button32" onAction="Button32_Click" screentip="资料检查" supertip="资料检查" label="资料检查&#xA;" size="large" getImage="GetInfoSetImage" getEnabled="GetGenerateEnabled"/>
					<button id="button17" onAction="Button17_Click" screentip="生成" supertip="设置你要生成可研报告的具体细节" label="生成&#xA;" size="large" getImage="GetInfoSetImage" enabled="false" visible="false"/>
					<button id="button29" onAction="button29_Click" screentip="快速生成" supertip="根据信息设置要求，生成可研报告的内容" label="快速生成&#xA;" size="large" getImage="GetAllImage" getEnabled="GetGenerateEnabled"/>	

				</group>
				<group id="group4" label="更多">
					<button id="button31" onAction="button31_Click" screentip="模板下载" supertip="模板下载" label="模板下载&#xA;" size="large" getImage="GetDownloadImage" enabled="true"/>
					<button id="button18" onAction="Button18_Click" screentip="虚拟顾问" supertip="在Word右侧打开一个Panel显示虚拟顾问" label="虚拟顾问&#xA;" size="large" enabled="true" getImage="GetCopilotImage"/>
					<button id="button19" onAction="Button19_Click" screentip="EA导览" supertip="在Word右侧打开一个Panel显示企业架构导览" label="EA导览&#xA;" size="large" getImage="GetExlinkImage" enabled="true"/>
					<button id="button20" onAction="Button20_Click" screentip="设置" supertip="文件保存路径、检查更新" label="设置&#xA;" size="large" enabled="true" getImage="GetSetImage"/>
					<button id="button21" onAction="Button21_Click" screentip="帮助" supertip="帮助中心" label="帮助&#xA;" size="large" enabled="true" getImage="GetHelpImage"/>
					<button id="button22" onAction="Button22_Click" screentip="反馈" supertip="反馈您遇上的问题" label="反馈&#xA;" size="large" getImage="GetFeedbackImage" enabled="true" visible="false"/>
					<button id="button23" onAction="Button23_Click" screentip="关于" supertip="插件版本、更新情况、开发人员" label="关于&#xA;" size="large" enabled="true" getImage="GetAboutImage"/>
				</group>
			</tab>
		</tabs>
	</ribbon>
	<contextMenus>
		<!-- 定义一个自定义的上下文菜单 -->
		<contextMenu idMso="ContextMenuText">
			<button id="CustomMenuItem-polish"
					label="优化"
					onAction="Button4_Click"
					imageMso="HappyFace" />
			<button id="CustomMenuItem-expand"
					label="扩写"
					onAction="Button5_Click"
					imageMso="FileNew" />
			<button id="CustomMenuItem-shorten"
					label="精简"
					onAction="Button6_Click"
					imageMso="Cut" />
		</contextMenu>
	</contextMenus>
</customUI>


