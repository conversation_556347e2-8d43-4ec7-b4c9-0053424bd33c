<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>选择生成目录</title>
        <style>
			body {
				font-family: 'Roboto', sans-serif;
				background-color: #fff;
				margin: 0;
				padding: 0;
				color: #333;
			}

			.dashboard {
				display: flex;
				width: 100%;
				margin: 0;
				background-color: #ffffff;
				border-radius: 10px;
				overflow: hidden;
			}

			.main-content {
				flex-grow: 1;
				padding: 20px;
			}

			.card {
				position: relative;
				background-color: #fff;
				border-radius: 8px;
				padding: 20px;
			}

			/* 创建双栏布局容器 */
			.two-columns-container {
				display: flex;
				flex-wrap: wrap;
				gap: 15px;
				margin-bottom: 15px;
			}

			.blocks {
				position: relative;
				width: calc(50% - 7.5px);
				display: flex;
				align-items: center;
				min-height: 36px;
			}
			
			/* 添加新的样式用于系统部署方式 */
			.block-select-11 {
				width: calc(50% - 7.5px); /* 保持和其他块一样的宽度 */
			}
			
			.block-select-11 .selects {
				font-size: 13px; /* 稍微减小字体大小 */
				padding-right: 28px; /* 减小右侧padding，为下拉箭头留出空间 */
			}

			/* 安全技术方案的块级元素 */
			.block-input-0 {
				width: 100%;
				margin-bottom: 20px;
			}

			.titles {
				color: #4a90e2;
				width: 120px;
				font-size: 14px;
				flex-shrink: 0;
			}

			.selects, .inputs {
				flex: 1;
				height: 36px;
				border: 1px solid #e0e0e0;
				border-radius: 4px;
				padding: 0 12px;
				font-size: 14px;
				color: #333;
				background-color: #fff;
				outline: none;
				transition: all 0.3s;
			}

			.selects {
				appearance: none;
				background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
				background-repeat: no-repeat;
				background-position: right 12px center;
				background-size: 16px;
				padding-right: 36px;
			}

			.selects:hover, .inputs:hover {
				border-color: #4a90e2;
			}

			.selects:focus, .inputs:focus {
				border-color: #4a90e2;
				box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
			}

			.btn-submit {
				display: block;
				width: 200px;
				height: 36px;
				margin: 30px auto 0;
				background: #4a90e2;
				color: #fff;
				border: none;
				border-radius: 4px;
				font-size: 14px;
				cursor: pointer;
				transition: background-color 0.3s;
			}

			.btn-submit:hover {
				background: #357abd;
			}

			/* 选中样式 */
			.selected-item .selects,
			.selected-item .inputs {
				border-color: #4a90e2;
				background-color: #f8f9ff;
			}

			/* 禁用样式 - 保持显示但改变样式 */
			.disabled-item {
				opacity: 0.7;
				pointer-events: none;
			}

			/* 下拉框禁用样式 */
			select:disabled, input:disabled {
				background-color: #f5f5f5;
				border-color: #e0e0e0;
				color: #999;
				cursor: not-allowed;
			}

			/* 占位符样式 */
			.selects option[value=""][disabled] {
				color: #999;
			}

			.inputs::placeholder {
				color: #999;
			}
		</style>
    </head>
    <body>
        <div class="dashboard">
            <div class="main-content">
                <div class="card">
					
					<div class="blocks block-input-0">
						<div class="titles">建设单位</div>
						<input class="inputs input-0" type="text" placeholder="请输入建设单位">
					</div>

                    <div class="two-columns-container">
                        <div class="blocks block-select-1">
                            <div class="titles">项目类型</div>
                            <select class="selects select-1">
                                <option value="" disabled selected>选择一个选项</option>
                                <option value="应用系统类">应用系统类</option>
                                <option value="技术支持平台">技术支持平台</option>
                                <option value="数据分析应用">数据分析应用</option>
                                <option value="移动应用类">移动应用类</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>

                        <div class="blocks block-select-3">
                            <div class="titles">项目分类</div>
                            <select class="selects select-3">
                                <option value="" disabled selected>选择一个选项</option>
                                <option value="信息系统建设与升级改造">信息系统建设与升级改造</option>
                                <option value="信息基础设施建设与升级改造">信息基础设施建设与升级改造</option>
                                <option value="信息安全防护体系建设与升级改造">信息安全防护体系建设与升级改造</option>
                                <option value="运行维护">运行维护</option>
                                <option value="信息专题研究">信息专题研究</option>
                            </select>
                        </div>

                        <div class="blocks block-select-7">
                            <div class="titles">建设性质</div>
                            <select class="selects select-7">
                                <option value="" disabled selected>选择一个选项</option>
                                <option value="新建">新建</option>
                                <option value="升级改造">升级改造</option>
                            </select>
                        </div>

                        <div class="blocks block-select-9">
                            <div class="titles">系统等保级别</div>
                            <select class="selects select-9">
                                <option value="" disabled selected>选择一个选项</option>
                                <option value="一级">一级</option>
                                <option value="二级">二级</option>
                                <option value="三级">三级</option>
                                <option value="四级">四级</option>
                            </select>
                        </div>

                        <div class="blocks block-input-10">
                            <div class="titles">系统用户数量</div>
                            <input class="inputs input-10" type="number" min="1" placeholder="请输入系统用户数量">
                        </div>

                        <div class="blocks block-select-11">
                            <div class="titles">系统部署方式</div>
                            <select class="selects select-11">
                                <option value="" disabled selected>选择一个选项</option>
								<option value="网一级部署模式">网一级部署模式</option>
								<option value="网省两级部署模式">网省两级部署模式</option>
								<option value="省一级部署模式">省一级部署模式</option>
								<option value="省地两级部署模式">省地两级部署模式</option>
								<option value="省地县三级部署模式">省地县三级部署模式</option>
								<option value="网一级管理节点和省一级分节点部署模式">网一级管理节点和省一级分节点部署模式</option>
                            </select>
                        </div>

                        <div class="blocks block-select-12">
                            <div class="titles">建设周期T（月）</div>
                            <select class="selects select-12">
                                <option value="" disabled selected>选择一个选项</option>
                                <option value="一年内">一年内</option>
                                <option value="一年以上两年内">一年以上两年内</option>
                                <option value="两年以上">两年以上</option>
                            </select>
                        </div>

                    </div>

                    <button type="button" class="btn-submit" onclick="submit()">确定</button>
                </div>
            </div>
        </div>
        <script>
            function handleProjectTypeChange() {
                const select3 = document.querySelector('.select-3');
                const dependentBlocks = [
                    '.block-select-1',
                    '.block-select-7',
                    '.block-select-9',
                    '.block-input-10',
                    '.block-select-11'
                ];
                
                const isInfoSystemType = select3.value === '信息系统建设与升级改造';
                
                dependentBlocks.forEach(selector => {
                    const block = document.querySelector(selector);
                    const element = block.querySelector('select, input');
                    
                    if (isInfoSystemType) {
                        block.style.display = 'flex';
                        element.disabled = false;
                    } else {
                        block.style.display = 'none';
                        element.disabled = true;
                        element.value = '';
                        block.classList.remove('selected-item');
                    }
                });
            }

            function handleItemSelection(element) {
                if (element.tagName === 'SELECT') {
                    const parentBlock = element.closest('.blocks');
                    if (element.value) {
                        parentBlock.classList.add('selected-item');
                    } else {
                        parentBlock.classList.remove('selected-item');
                    }
                } else if (element.tagName === 'INPUT') {
                    const parentBlock = element.closest('.blocks');
                    if (element.value.trim()) {
                        parentBlock.classList.add('selected-item');
                    } else {
                        parentBlock.classList.remove('selected-item');
                    }
                }
            }

            function submit() {
                const formData = {};
                const selects = document.querySelectorAll('select');
                const inputs = document.querySelectorAll('input');

                selects.forEach(select => {
                    if (select.value && !select.disabled) {
                        formData[select.className.split(' ')[1]] = select.options[select.selectedIndex].text;
                    }
                });

                inputs.forEach(input => {
                    if (input.value && !input.disabled) {
                        formData[input.className.split(' ')[1]] = input.value;
                    }
                });

                // 验证必填字段
                const select3 = document.querySelector('.select-3');
                const isInfoSystemType = select3.value === '信息系统建设与升级改造';

                if (isInfoSystemType) {
                    if (!formData['select-1'] || !formData['select-4'] || !formData['input-10']) {
                        alert('请填写必要的信息');
                        return;
                    }
                } else {
                    if (!formData['select-4']) {
                        alert('请填写必要的信息');
                        return;
                    }
                }

                // 发送数据到C#
                window.chrome.webview.postMessage(formData);
            }

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化时隐藏所有依赖字段
                const dependentBlocks = [
                    '.block-select-1',
                    '.block-select-7',
                    '.block-select-9',
                    '.block-input-10',
                    '.block-select-11'
                ];
                
                dependentBlocks.forEach(selector => {
                    const block = document.querySelector(selector);
                    block.style.display = 'none';
                    const element = block.querySelector('select, input');
                    element.disabled = true;
                });

                // 为项目分类添加变化监听
                const select3 = document.querySelector('.select-3');
                select3.addEventListener('change', handleProjectTypeChange);
                
                // 为所有可见的select和input添加change事件监听
                document.querySelectorAll('select, input').forEach(element => {
                    element.addEventListener('change', () => handleItemSelection(element));
                });
            });
        </script>
    </body>
</html>