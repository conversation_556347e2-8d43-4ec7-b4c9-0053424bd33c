<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .settings-container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            transition: all 0.3s ease;
        }

        .settings-title {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: #fff;
            font-size: 24px;
            font-weight: 700;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .form-group {
            margin-bottom: 20px;
        }

            .form-group label {
                display: block;
                margin-bottom: 10px;
                font-weight: 500;
                color: #333;
            }

            .form-group input[type="text"],
            .form-group select,
            .form-group input[type="password"] {
                width: 100%;
                padding: 12px;
                border: 1px solid rgba(74, 144, 226, 0.3);
                border-radius: 8px;
                font-size: 14px;
                transition: all 0.3s ease;
            }

                .form-group input[type="text"]:focus,
                .form-group select:focus,
                .form-group input[type="password"]:focus {
                    border-color: #4a90e2;
                    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
                    outline: none;
                }

            .form-group button {
                background: linear-gradient(45deg, #4a90e2, #63b3ed);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
            }

                .form-group button:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
                }

            .form-group .file-path-container {
                display: flex;
                align-items: center;
            }

        .options label {
            display: block;
            margin-bottom: 10px;
        }

        .options input[type="radio"] {
            margin-right: 10px;
        }

        .notification-settings {
            margin-top: 20px;
        }

            .notification-settings label {
                display: block;
                margin-bottom: 10px;
                font-weight: 500;
                color: #333;
            }

            .notification-settings input[type="checkbox"] {
                margin-right: 10px;
            }
    </style>
</head>
<body>

    <div class="settings-container">

        <div class="form-group">
            <label>默认文档保存位置</label>
            <div class="file-path-container">
                <input type="text" id="filePath" value="" readonly>
                <button onclick="chooseFilePath()">📁</button>
            </div>
        </div>
        <div class="form-group">
            <label>默认调用大模型</label>
            <select id="modelSelection">
                <option value="auto">
                    院内Qwen3大模型      
                </option>
            </select>
        </div>
        <!-- 添加保存按钮 -->
        <div class="form-group">
            <button onclick="saveSettings()" style="width: 100%;">保存设置</button>
        </div>
    </div>

    <script>
        // 监听来自C#的消息
        window.chrome.webview.addEventListener('message', function (event) {
            const message = event.data;
            console.log('收到消息:', message);

            switch (message.action) {
                case 'folderSelected':
                    document.getElementById('filePath').value = message.path;
                    break;
                case 'settingsSaved':
                    if (message.success) {
                        console.log('设置保存成功！');
                        // 添加视觉反馈
                        showNotification('设置已保存成功！', 'success');
                    } else {
                        alert('设置保存失败！');
                    }
                    break;
                case 'loadSettings':
                    if (message.settings) {
                        console.log('加载设置:', message.settings);
                        // 应用保存的设置
                        let filePath = message.settings.FilePath || '';

                        console.log('设置文件路径为:', filePath);

                        // 显示用户友好的路径
                        const filePathInput = document.getElementById('filePath');
                        if (filePathInput) {
                            filePathInput.value = filePath;
                            console.log('路径已设置到输入框:', filePathInput.value);
                        } else {
                            console.error('找不到filePath输入框元素');
                        }

                        // 加载大模型选择
                        if (message.settings.ModelSelection) {
                            const modelSelect = document.getElementById('modelSelection');
                            if (modelSelect) {
                                modelSelect.value = message.settings.ModelSelection;
                                console.log('模型选择已设置为:', message.settings.ModelSelection);
                            }
                        }
                    } else {
                        console.error('没有收到设置数据');
                    }
                    break;
            }
        });

        // 添加通知函数
        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            // 设置样式
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.left = '50%';
            notification.style.transform = 'translateX(-50%)';
            notification.style.padding = '12px 24px';
            notification.style.borderRadius = '8px';
            notification.style.backgroundColor = type === 'success' ? '#4CAF50' : '#F44336';
            notification.style.color = 'white';
            notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
            notification.style.zIndex = '1000';
            notification.style.opacity = '0';
            notification.style.transition = 'opacity 0.3s ease';

            // 添加到页面
            document.body.appendChild(notification);

            // 显示通知
            setTimeout(() => {
                notification.style.opacity = '1';
            }, 10);

            // 3秒后隐藏通知
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 其他函数保持不变
        async function chooseFilePath() {
            window.chrome.webview.postMessage({
                action: "selectFolder"
            });
        }

        async function saveSettings() {
            const filePath = document.getElementById("filePath").value;
            const modelSelection = document.getElementById("modelSelection").value;

            window.chrome.webview.postMessage({
                action: "saveSettings",
                filePath: filePath,
                modelSelection: modelSelection
            });
        }
    </script>

</body>
</html>